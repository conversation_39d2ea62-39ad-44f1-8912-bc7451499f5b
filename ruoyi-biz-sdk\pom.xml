<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.ruoyi</groupId>
		<artifactId>ruoyi</artifactId>
		<version>3.9.0</version>
	</parent>

	<artifactId>ruoyi-biz-sdk</artifactId>
	<version>1.0.1</version>
	<name>ruoyi-biz-sdk</name>

	<properties>
		<maven.compiler.source>8</maven.compiler.source>
		<maven.compiler.target>8</maven.compiler.target>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<tools.version>1.0.1</tools.version>
	</properties>
	<dependencies>
		<dependency>
			<groupId>com.ruoyi</groupId>
			<artifactId>ruoyi-common</artifactId>
		</dependency>
		<dependency>
			<groupId>com.ruoyi</groupId>
			<artifactId>ruoyi-framework</artifactId>
		</dependency>
		<dependency>
			<groupId>com.ruoyi</groupId>
			<artifactId>ruoyi-tools</artifactId>
			<version>${tools.version}</version>
		</dependency>
		<dependency>
			<groupId>com.ruoyi</groupId>
			<artifactId>ruoyi-flowable</artifactId>
			<version>1.0.1</version>
		</dependency>
		<dependency>
			<groupId>com.ruoyi</groupId>
			<artifactId>ruoyi-mq-async</artifactId>
			<version>1.0.1</version>
		</dependency>
		<dependency>
			<groupId>com.ruoyi</groupId>
			<artifactId>ruoyi-template</artifactId>
			<version>1.0.1</version>
		</dependency>
		<dependency>
			<groupId>com.ruoyi</groupId>
			<artifactId>ruoyi-todo</artifactId>
			<version>1.0.1</version>
		</dependency>
	</dependencies>
</project>
